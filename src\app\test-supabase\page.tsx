'use client'

import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'

export default function TestSupabase() {
  const [connectionStatus, setConnectionStatus] = useState<string>('Testing...')
  const [projectInfo, setProjectInfo] = useState<any>(null)

  useEffect(() => {
    async function testConnection() {
      try {
        // Test the connection by getting the current session
        const { data, error } = await supabase.auth.getSession()
        
        if (error) {
          setConnectionStatus(`Error: ${error.message}`)
        } else {
          setConnectionStatus('✅ Connected to Supabase successfully!')
          
          // Get some basic project info
          const { data: { user } } = await supabase.auth.getUser()
          setProjectInfo({
            url: supabase.supabaseUrl,
            hasUser: !!user,
            timestamp: new Date().toISOString()
          })
        }
      } catch (err) {
        setConnectionStatus(`Connection failed: ${err}`)
      }
    }

    testConnection()
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">
          Supabase Connection Test
        </h1>
        
        <div className="space-y-4">
          <div>
            <h2 className="text-lg font-semibold text-gray-700">Status:</h2>
            <p className="text-sm text-gray-600">{connectionStatus}</p>
          </div>
          
          {projectInfo && (
            <div>
              <h2 className="text-lg font-semibold text-gray-700">Project Info:</h2>
              <div className="text-sm text-gray-600 space-y-1">
                <p><strong>URL:</strong> {projectInfo.url}</p>
                <p><strong>User Authenticated:</strong> {projectInfo.hasUser ? 'Yes' : 'No'}</p>
                <p><strong>Test Time:</strong> {projectInfo.timestamp}</p>
              </div>
            </div>
          )}
          
          <div className="pt-4">
            <a 
              href="/"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              ← Back to Home
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
